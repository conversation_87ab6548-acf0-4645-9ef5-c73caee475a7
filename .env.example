# Make sure to change this to your own random string of 32 characters (https://docs.typebot.io/self-hosting/deploy/docker#2-add-the-required-configuration)
ENCRYPTION_SECRET=do+UspMmB/rewbX2K/rskFmtgGSSZ8Ta

DATABASE_URL=*********************************************/typebot

NODE_OPTIONS=--no-node-snapshot

# Required: Your public URLs
# Option 1: With your own domain (RECOMMENDED)
# NEXTAUTH_URL=https://typebot.yourdomain.com
# NEXT_PUBLIC_VIEWER_URL=https://bot.yourdomain.com

# Option 2: Using server domain (CURRENT SETUP)
# NEXTAUTH_URL=http://ganga.merai.cloud:7070
# NEXT_PUBLIC_VIEWER_URL=http://ganga.merai.cloud:7071

# Option 3: Local development
NEXTAUTH_URL=http://localhost:7070
NEXT_PUBLIC_VIEWER_URL=http://localhost:7071

# Admin email (will get unlimited plan)
ADMIN_EMAIL=<EMAIL>

# GitLab OAuth Configuration
GITLAB_CLIENT_ID=f6692a2a7622936d75ccbfdbdd3714b0fec824b1863188c3888da8e954620399
GITLAB_CLIENT_SECRET=gloas-f6692a2a7622936d75ccbfdbdd3714b0fec824b1863188c3888da8e954620399
GITLAB_BASE_URL=https://git.merai.app
GITLAB_NAME=Merai GitLab

# Optional: Restrict access to specific GitLab groups (comma-separated)
# GITLAB_REQUIRED_GROUPS=your-group-1,your-group-2

# For more configuration options check out: https://docs.typebot.io/self-hosting/configuration
