# Make sure to change this to your own random string of 32 characters (https://docs.typebot.io/self-hosting/deploy/docker#2-add-the-required-configuration)
ENCRYPTION_SECRET=do+UspMmB/rewbX2K/rskFmtgGSSZ8Ta

DATABASE_URL=*********************************************/typebot

NODE_OPTIONS=--no-node-snapshot

# Required: Your public URLs
NEXTAUTH_URL=https://your-typebot-domain.com
NEXT_PUBLIC_VIEWER_URL=https://your-bot-domain.com

# Admin email (will get unlimited plan)
ADMIN_EMAIL=<EMAIL>

# GitLab OAuth Configuration
GITLAB_CLIENT_ID=25e47e2edc77670ff29b8c179dae95599a1db16bfb9f0ccc32dd45b82e9e63f8
GITLAB_CLIENT_SECRET=gloas-9b350b316bd8e26482c7bc9d7d02c23de8f786c2d7c3b0afc52ba23664c53621
GITLAB_BASE_URL=https://git.merai.app
GITLAB_NAME=Merai GitLab

# Optional: Restrict access to specific GitLab groups (comma-separated)
# GITLAB_REQUIRED_GROUPS=your-group-1,your-group-2

# For more configuration options check out: https://docs.typebot.io/self-hosting/configuration
