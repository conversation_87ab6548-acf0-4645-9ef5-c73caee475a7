{"name": "landing-page", "version": "0.0.1", "type": "module", "private": true, "scripts": {"dev": "dotenv -e ./.env -e ../../.env -- vinxi dev --port 3003", "build": "bun check-broken-links && dotenv -e ./.env -e ../../.env -- vinxi build", "start": "dotenv -e ./.env -e ../../.env -- vinxi start", "check-broken-links": "mdx-local-link-checker content public '*/(index.mdx|blog|about|pricing?isTiersModalOpened=true)'"}, "dependencies": {"@ark-ui/react": "5.7.0", "@tanstack/react-router": "1.120.5", "@tanstack/react-start": "1.120.5", "@typebot.io/billing": "workspace:*", "@typebot.io/lib": "workspace:*", "@typebot.io/prisma": "workspace:*", "@typebot.io/react": "workspace:*", "motion": "12.4.3", "react": "18.3.1", "react-dom": "18.3.1", "@typebot.io/zod": "workspace:*", "@typebot.io/ui": "workspace:*"}, "devDependencies": {"@content-collections/core": "0.8.0", "@content-collections/mdx": "0.2.0", "@content-collections/vinxi": "0.1.0", "@shikijs/transformers": "2.4.2", "@tailwindcss/typography": "0.5.16", "@tanstack/react-router-devtools": "1.115.0", "@typebot.io/tsconfig": "workspace:*", "unist-util-visit": "5.0.0", "@types/react": "18.3.18", "@types/react-dom": "18.3.5", "autoprefixer": "10.4.20", "dotenv-cli": "8.0.0", "mdx-local-link-checker": "2.1.1", "postcss": "8.5.3", "rehype-autolink-headings": "7.1.0", "rehype-pretty-code": "0.14.0", "rehype-slug": "6.0.0", "remark-gfm": "4.0.1", "tailwindcss": "3.4.17", "tailwindcss-motion": "1.1.0", "unified": "11.0.5", "vite-tsconfig-paths": "5.1.4"}}