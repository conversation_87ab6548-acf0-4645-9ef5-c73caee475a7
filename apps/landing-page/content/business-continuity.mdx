---
title: Business Continuity & Long-Term Commitment
author: baptistearno
---

## Built for the Long Term

Typebot has been actively developed and maintained since **2020**. Over several years of continuous progress. It is not a short-lived experiment or VC-fueled sprint. It's a sustainable, founder-led product with real users and long-term vision.

- 📈 Bootstrapped & profitable, no dependency on external funding
- 🛠 Thoughtfully built with long-term stability in mind
- 👤 Led by [<PERSON>](https://www.linkedin.com/in/baptistearno), with support from part-time collaborators

## Fair Source Licensed for User Freedom & Sustainability

Typebot is released under the [**Functional Source License (FSL)**](https://docs.typebot.io/self-hosting/get-started#license-requirements), a modern, fair-source model designed to balance open access and business sustainability.

- Full source code available for inspection, learning, and contribution
- **Converts to Apache 2.0 after two years**
- Prevents harmful free-riding while protecting developer incentives
- Enables **self-hosting** and internal customization without vendor lock-in

This license supports transparency, freedom, and future-proofing. Especially important for enterprise adoption.

## Enterprise-Ready Continuity Measures

To support long-term use in enterprise environments, I offer:

- **ISO 27001 Compliance**: Typebot adheres to ISO 27001 standards, including mandatory business continuity, operational resilience, and incident response processes, all audited and documented.
- **Operational Escrow**: While the source code is already public, I offer an optional escrow agreement to ensure your company receives deployment scripts, infrastructure snapshots, and credentials should I become unavailable.
- **Formal SLA**: Service agreements with uptime guarantees, response time, and support channels  

## Founder Commitment

As the founder, Typebot is my full-time, long-term focus. I've been working on it for a long time with no plans to stop. My goal is to build a sustainable, reliable product. Not pursue rapid exits or risky pivots.

Unlike many venture-backed startups, there’s no outside pressure influencing the direction or viability of the project.

You're not just adopting a tool, you're investing in a stable, dependable solution built to last.

## Let’s Talk

If you’d like to review:
- A formal SLA
- A business continuity plan

I’d be happy to provide them. Book an Enterprise plan meeting with me [here](https://typebot.io/enterprise-lead-form).

Baptiste Arnaud,<br />Founder of Typebot  
