{"name": "@typebot.io/react", "version": "0.7.2", "description": "Convenient library to display typebots on your React app", "license": "FSL-1.1-ALv2", "type": "module", "main": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./commands": {"types": "./dist/commands.d.ts", "default": "./dist/commands.js"}}, "scripts": {"dev": "tsup --watch", "build": "tsup"}, "dependencies": {"react": "18.3.1", "@typebot.io/js": "workspace:*"}, "devDependencies": {"@typebot.io/tsconfig": "workspace:*", "@types/react": "18.3.18", "tsup": "8.5.0"}, "peerDependencies": {"react": "^16.0.0 || ^17.0.0 || ^18.0.0"}, "publishConfig": {"access": "public"}}