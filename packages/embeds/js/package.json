{"name": "@typebot.io/js", "version": "0.7.2", "description": "Javascript library to display typebots on your website", "license": "FSL-1.1-ALv2", "type": "module", "main": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {".": "./dist/index.js", "./web": "./dist/web.js"}, "scripts": {"dev": "tsup --watch", "build": "tsup --metafile"}, "devDependencies": {"@ai-sdk/ui-utils": "1.2.2", "@ark-ui/solid": "5.0.0", "@fix-webm-duration/fix": "1.0.1", "@stripe/stripe-js": "1.54.1", "@tailwindcss/container-queries": "0.1.1", "@typebot.io/blocks-bubbles": "workspace:*", "@typebot.io/blocks-inputs": "workspace:*", "@typebot.io/blocks-integrations": "workspace:*", "@typebot.io/blocks-logic": "workspace:*", "@typebot.io/chat-api": "workspace:*", "@typebot.io/env": "workspace:*", "@typebot.io/lib": "workspace:*", "@typebot.io/logs": "workspace:*", "@typebot.io/rich-text": "workspace:*", "@typebot.io/schemas": "workspace:*", "@typebot.io/settings": "workspace:*", "@typebot.io/theme": "workspace:*", "@typebot.io/tsconfig": "workspace:*", "@typebot.io/ui": "workspace:*", "@typebot.io/zendesk-block": "workspace:*", "@types/dompurify": "3.2.0", "autoprefixer": "10.4.20", "dompurify": "3.2.4", "esbuild-plugin-solid": "^0.6.0", "ky": "1.2.4", "marked": "9.0.3", "partysocket": "1.0.2", "postcss": "8.5.3", "solid-element": "1.9.1", "solid-js": "1.9.5", "tailwindcss": "3.4.17", "tsup": "8.5.0"}, "publishConfig": {"access": "public"}}