@tailwind base;
@tailwind components;
@tailwind utilities;

/* Hide scrollbar for Chrome, Safari and Opera */
.scrollable-container::-webkit-scrollbar {
  display: none;
}

/* Hide scrollbar for IE, Edge and Firefox */
.scrollable-container {
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
}

.text-fade-in {
  transition: opacity 400ms ease-in 200ms;
}

.bubble-typing {
  transition: width 400ms ease-out, height 400ms ease-out;
}

.bubble1,
.bubble2,
.bubble3 {
  background-color: var(--typebot-host-bubble-color);
  opacity: 0.5;
}

.bubble1 {
  animation: chatBubbles 1s ease-in-out infinite;
}

.bubble2 {
  animation: chatBubbles 1s ease-in-out infinite;
  animation-delay: 0.3s;
}

.bubble3 {
  animation: chatBubbles 1s ease-in-out infinite;
  animation-delay: 0.5s;
}

@keyframes chatBubbles {
  0% {
    transform: translateY(2.5);
  }
  50% {
    transform: translateY(-2.5px);
  }
  100% {
    transform: translateY(0);
  }
}

button,
input,
textarea {
  font-weight: 300;
}

a {
  text-decoration: underline;
}

ul,
ol {
  margin-inline-start: 0px;
  margin-inline-end: 0px;
  padding-inline-start: 40px;
}

ol {
  list-style-type: decimal;
}

ul {
  list-style-type: disc;
}

li:not(:last-child) {
  margin-bottom: 8px;
}

pre {
  white-space: pre-wrap;
  word-wrap: break-word;
  overflow-wrap: break-word;
  max-height: 100%;
  max-width: 100%;
  overflow: auto;
}

.slate-bold {
  font-weight: bold;
}

.slate-italic {
  font-style: oblique;
}

.slate-underline {
  text-decoration: underline;
}

.text-input::-webkit-input-placeholder {
  color: var(--typebot-input-placeholder-color) !important;
  opacity: 1 !important;
}
.text-input::-moz-placeholder {
  color: var(--typebot-input-placeholder-color) !important;
  opacity: 1 !important;
}
.text-input::placeholder {
  color: var(--typebot-input-placeholder-color) !important;
  opacity: 1 !important;
}

.typebot-container {
  background-image: var(--typebot-container-bg-image);
  background-color: var(--typebot-container-bg-color);
  background-position: center;
  background-size: cover;
  font-family: var(--typebot-container-font-family), -apple-system,
    BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif,
    "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
  container-type: size;
}

.typebot-chat-view {
  @apply pt-5 px-3;
  background-color: rgba(
    var(--typebot-chat-container-bg-rgb),
    var(--typebot-chat-container-opacity)
  );
  color: rgb(var(--typebot-chat-container-color));
  backdrop-filter: blur(var(--typebot-chat-container-blur));
  border-width: var(--typebot-chat-container-border-width);
  border-color: rgba(
    var(--typebot-chat-container-border-rgb),
    var(--typebot-chat-container-border-opacity)
  );
  box-shadow: var(--typebot-chat-container-box-shadow);
}

@container (min-height: 500px) {
  .typebot-chat-view {
    @apply pt-10;
  }
}

@container (min-height: 300px) {
  .typebot-bottom-spacer {
    @apply h-[20%];
  }
}

.typebot-selectable {
  border-width: var(--typebot-button-border-width);
  border-color: rgba(
    var(--typebot-button-border-rgb),
    calc(var(--selectable-alpha-ratio) * 0.25)
  );
  border-radius: var(--typebot-button-border-radius);
  color: rgb(var(--typebot-chat-container-color));
  background-color: rgba(
    var(--typebot-button-bg-rgb),
    calc(var(--selectable-alpha-ratio) * 0.08)
  );
  transition: all 0.3s ease;
}

.typebot-selectable:hover {
  background-color: rgba(
    var(--typebot-button-bg-rgb),
    calc(var(--selectable-alpha-ratio) * 0.12)
  );
  border-color: rgba(
    var(--typebot-button-border-rgb),
    calc(var(--selectable-alpha-ratio) * 0.3)
  );
}

.typebot-selectable.selected {
  background-color: rgba(
    var(--typebot-button-bg-rgb),
    calc(var(--selectable-alpha-ratio) * 0.18)
  );
  border-color: rgba(
    var(--typebot-button-border-rgb),
    calc(var(--selectable-alpha-ratio) * 0.35)
  );
}

.typebot-checkbox {
  border: 1px solid
    rgba(var(--typebot-button-bg-rgb), var(--typebot-button-opacity));
  border-radius: var(--typebot-button-border-radius);
  background-color: rgba(var(--typebot-checkbox-bg-rgb));
  color: var(--typebot-button-color);
  padding: 1px;
  border-radius: 2px;
  transition: all 0.3s ease;
}

.typebot-checkbox.checked {
  background-color: rgb(var(--typebot-button-bg-rgb));
}

.typebot-host-bubble {
  color: var(--typebot-host-bubble-color);
}

.typebot-host-bubble > .bubble-typing {
  background-color: rgba(
    var(--typebot-host-bubble-bg-rgb),
    var(--typebot-host-bubble-opacity)
  );
  border-width: var(--typebot-host-bubble-border-width);
  border-color: rgba(
    var(--typebot-host-bubble-border-rgb),
    var(--typebot-host-bubble-border-opacity)
  );
  border-radius: var(--typebot-host-bubble-border-radius);
  box-shadow: var(--typebot-host-bubble-box-shadow);
  backdrop-filter: blur(var(--typebot-host-bubble-blur));
}

.typebot-host-bubble img,
.typebot-host-bubble video,
.typebot-host-bubble iframe {
  border-radius: 6px;
}

.typebot-guest-bubble {
  color: var(--typebot-guest-bubble-color);
  background-color: rgba(
    var(--typebot-guest-bubble-bg-rgb),
    var(--typebot-guest-bubble-opacity)
  );
  border-width: var(--typebot-guest-bubble-border-width);
  border-color: rgba(
    var(--typebot-guest-bubble-border-rgb),
    var(--typebot-guest-bubble-border-opacity)
  );
  border-radius: var(--typebot-guest-bubble-border-radius);
  box-shadow: var(--typebot-guest-bubble-box-shadow);
  backdrop-filter: blur(var(--typebot-guest-bubble-blur));
}

.typebot-guest-bubble-image-attachment {
  border-radius: var(--typebot-guest-bubble-border-radius);
}

.typebot-input {
  color: var(--typebot-input-color);
  background-color: rgba(
    var(--typebot-input-bg-rgb),
    var(--typebot-input-opacity)
  );
  border-width: var(--typebot-input-border-width);
  border-color: rgba(
    var(--typebot-input-border-rgb),
    var(--typebot-input-border-opacity)
  );
  border-radius: var(--typebot-input-border-radius);
  box-shadow: var(--typebot-input-box-shadow);
  backdrop-filter: blur(var(--typebot-input-blur));
  transition: filter 100ms ease;
}

.typebot-input-error-message {
  color: var(--typebot-input-color);
}

.typebot-input-form .typebot-button {
  box-shadow: var(--typebot-input-box-shadow);
}

.typebot-button > .send-icon {
  fill: var(--typebot-button-color);
}

.ping span {
  background-color: rgb(var(--typebot-button-bg-rgb));
}

.rating-icon-container svg {
  width: 42px;
  height: 42px;
  stroke: rgb(var(--typebot-button-bg-rgb));
  fill: rgba(
    var(--typebot-host-bubble-bg-rgb),
    var(--typebot-host-bubble-opacity)
  );
  transition: fill 100ms ease-out;
}

.rating-icon-container.selected svg {
  fill: rgb(var(--typebot-button-bg-rgb));
}

.rating-icon-container:hover svg {
  filter: brightness(0.9);
}

.rating-icon-container:active svg {
  filter: brightness(0.75);
}

.upload-progress-bar {
  background-color: rgb(var(--typebot-button-bg-rgb));
  border-radius: var(--typebot-input-border-radius);
}

.total-files-indicator {
  background-color: rgb(var(--typebot-button-bg-rgb));
  color: var(--typebot-button-color);
  font-size: 10px;
}

.typebot-upload-input {
  transition: border-color 100ms ease-out;
  border-radius: var(--typebot-input-border-radius);
}

.typebot-upload-input.dragging-over {
  border-color: rgb(var(--typebot-button-bg-rgb));
}

.typebot-country-select {
  color: var(--typebot-input-color);
  background-color: var(--typebot-input-bg-color);
  border-radius: var(--typebot-button-border-radius);
}

.typebot-datetime-input {
  color-scheme: light;
  color: var(--typebot-input-color);
  background-color: var(--typebot-input-bg-color);
  border-radius: var(--typebot-input-border-radius);
}

.typebot-picture-button {
  color: var(--typebot-button-color);
  background-color: rgb(var(--typebot-button-bg-rgb));
  border-radius: var(--typebot-button-border-radius);
  transition: all 0.3s ease;
  width: 236px;
}

.typebot-picture-button > img,
.typebot-selectable-picture > img {
  border-radius: var(--typebot-button-border-radius)
    var(--typebot-button-border-radius) 0 0;
  min-width: 200px;
  width: 100%;
  max-height: 200px;
  height: 100%;
  object-fit: cover;
}

.typebot-picture-button.has-svg > img,
.typebot-selectable-picture.has-svg > img {
  max-height: 128px;
  object-fit: contain;
  padding: 1rem;
}

.typebot-selectable-picture {
  border: 1px solid
    rgba(
      var(--typebot-button-bg-rgb),
      calc(var(--selectable-alpha-ratio) * 0.25)
    );
  border-radius: var(--typebot-button-border-radius);
  color: rgb(var(--typebot-chat-container-color));
  background-color: rgba(
    var(--typebot-button-bg-rgb),
    calc(var(--selectable-alpha-ratio) * 0.08)
  );
  transition: all 0.3s ease;
  width: 236px;
}

.typebot-selectable-picture:hover {
  background-color: rgba(
    var(--typebot-button-bg-rgb),
    calc(var(--selectable-alpha-ratio) * 0.12)
  );
  border-color: rgba(
    var(--typebot-button-bg-rgb),
    calc(var(--selectable-alpha-ratio) * 0.3)
  );
}

.typebot-selectable-picture.selected {
  background-color: rgba(
    var(--typebot-button-bg-rgb),
    calc(var(--selectable-alpha-ratio) * 0.18)
  );
  border-color: rgba(
    var(--typebot-button-bg-rgb),
    calc(var(--selectable-alpha-ratio) * 0.35)
  );
}

select option {
  color: var(--typebot-input-color);
  background-color: var(--typebot-input-bg-color);
}

.typebot-progress-bar-container {
  background-color: rgba(
    var(--typebot-progress-bar-bg-rgb),
    calc(var(--selectable-alpha-ratio) * 0.12)
  );

  height: var(--typebot-progress-bar-height);
  position: var(--typebot-progress-bar-position);
  top: var(--typebot-progress-bar-top);
  bottom: var(--typebot-progress-bar-bottom);
  left: 0;
  width: 100%;
  z-index: 42424242;
}

.typebot-progress-bar-container > .typebot-progress-bar {
  background-color: var(--typebot-progress-bar-color);
  position: absolute;
  height: 100%;
  transition: width 0.25s ease;
}

.typebot-recorder .left-gradient {
  background-image: linear-gradient(
    to right,
    rgba(var(--typebot-input-bg-rgb), 1),
    rgba(var(--typebot-input-bg-rgb), 0)
  );
}

.typebot-recorder .right-gradient {
  background-image: linear-gradient(
    to left,
    rgba(var(--typebot-input-bg-rgb), 1),
    rgba(var(--typebot-input-bg-rgb), 0)
  );
}

.typebot-recorder button {
  color: rgba(var(--typebot-button-bg-rgb));
  background-color: rgba(var(--typebot-button-bg-rgb), 0.3);
}

@keyframes fadeInFromTop {
  0% {
    opacity: 0;
    transform: translateY(-4px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeOutFromTop {
  0% {
    opacity: 1;
    transform: translateY(0);
  }

  100% {
    opacity: 0;
    transform: translateY(-4px);
  }
}

@keyframes fadeInFromBottom {
  0% {
    opacity: 0;
    transform: translateY(4px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeOutFromBottom {
  0% {
    opacity: 1;
    transform: translateY(0);
  }

  100% {
    opacity: 0;
    transform: translateY(4px);
  }
}

[data-scope="menu"][data-part="content"] {
  color: var(--typebot-input-color);
  background-color: rgba(
    var(--typebot-input-bg-rgb),
    var(--typebot-input-opacity)
  );
  border-width: var(--typebot-input-border-width);
  border-color: rgba(
    var(--typebot-input-border-rgb),
    var(--typebot-input-border-opacity)
  );
  border-radius: var(--typebot-input-border-radius);
  box-shadow: var(--typebot-input-box-shadow);
  backdrop-filter: blur(var(--typebot-input-blur));
}

[data-scope="menu"][data-part="item"] {
  cursor: pointer;
  background-color: rgba(
    var(--typebot-input-bg-rgb),
    var(--typebot-input-opacity)
  );
  border-radius: var(--typebot-input-border-radius);
}

[data-scope="menu"][data-part="content"][data-state="open"] {
  animation: fadeInFromTop 150ms ease-out forwards;
}

[data-scope="menu"][data-part="content"][data-state="closed"] {
  animation: fadeOutFromTop 50ms ease-out forwards;
}

[data-scope="progress"][data-part="root"] {
  width: 100%;
  height: 100%;
}

[data-scope="progress"][data-part="circle"] {
  --size: 40px;
  --thickness: 4px;
  --radius: calc(40px / 2 - 4px / 2);
  --circomference: calc(2 * 3.14159 * calc(40px / 2 - 4px / 2));
}

[data-scope="progress"][data-part="circle-range"] {
  stroke: white;
  --transition-prop: stroke-dasharray, stroke, stroke-dashoffset;
  transition-property: stroke-dasharray, stroke, stroke-dashoffset;
  --transition-duration: 0.2s;
  transition-duration: 0.2s;
}

[data-scope="progress"][data-part="circle-track"] {
  stroke: rgba(0, 0, 0, 0.5);
}

.typebot-buttons-input > [data-slot="list"] {
  flex-direction: var(--typebot-buttons-input-flex-direction);
  flex-wrap: wrap;
}

.typebot-buttons-input > .typebot-input {
  max-width: 350px;
}
