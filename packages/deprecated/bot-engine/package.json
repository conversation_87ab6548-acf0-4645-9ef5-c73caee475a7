{"name": "bot-engine", "private": true, "license": "FSL-1.1-ALv2", "version": "0.1.0", "type": "module", "scripts": {"build": "tsup", "dev": "tsup --watch"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}}, "dependencies": {"@stripe/react-stripe-js": "1.16.4", "@stripe/stripe-js": "1.54.1", "@typebot.io/blocks-bubbles": "workspace:*", "@typebot.io/blocks-core": "workspace:*", "@typebot.io/blocks-inputs": "workspace:*", "@typebot.io/blocks-integrations": "workspace:*", "@typebot.io/blocks-logic": "workspace:*", "@typebot.io/conditions": "workspace:*", "@typebot.io/logs": "workspace:*", "@typebot.io/env": "workspace:*", "@typebot.io/groups": "workspace:*", "@typebot.io/lib": "workspace:*", "@typebot.io/prisma": "workspace:*", "@typebot.io/results": "workspace:*", "@typebot.io/settings": "workspace:*", "@typebot.io/theme": "workspace:*", "@typebot.io/typebot": "workspace:*", "@typebot.io/variables": "workspace:*", "qs": "6.11.2", "react": "18.3.1", "react-dom": "18.3.1", "react-phone-number-input": "3.2.16", "react-scroll": "1.8.9", "react-transition-group": "4.4.5", "resize-observer": "1.0.4", "typebot-js": "workspace:*"}, "devDependencies": {"@typebot.io/tsconfig": "workspace:*", "@types/qs": "6.9.7", "@types/react": "18.3.18", "@types/react-phone-number-input": "3.0.14", "@types/react-scroll": "1.8.6", "@types/react-transition-group": "4.4.5", "autoprefixer": "10.4.20", "postcss": "8.5.3", "tailwindcss": "3.4.17", "tsup": "8.5.0"}, "peerDependencies": {"@typebot.io/prisma": "workspace:*", "react": "^16.0.0 || ^17.0.0 || ^18.0.0"}}