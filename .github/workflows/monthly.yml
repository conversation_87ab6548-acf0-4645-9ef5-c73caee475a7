name: Monthly job

on:
  schedule:
    - cron: "30 6 1 * *"

jobs:
  clean:
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: ./packages/scripts
    env:
      DATABASE_URL: "${{ secrets.DATABASE_URL }}"
      DATABASE_URL_REPLICA: "${{ secrets.DATABASE_URL_REPLICA }}"
      ENCRYPTION_SECRET: "${{ secrets.ENCRYPTION_SECRET }}"
      NEXTAUTH_URL: "http://localhost:3000"
      NEXT_PUBLIC_VIEWER_URL: "http://localhost:3001"
    steps:
      - uses: actions/checkout@v2
      - uses: oven-sh/setup-bun@v2
      - run: bun install
      - run: bunx turbo run cron:monthly
