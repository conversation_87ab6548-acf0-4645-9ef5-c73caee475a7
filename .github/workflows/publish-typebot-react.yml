name: Publish @typebot.io/react package to NPM

on:
  push:
    tags:
      - "react-v*"

jobs:
  publish:
    runs-on: ubuntu-latest
    env:
      NPM_TOKEN: ${{ secrets.NPM_TOKEN }}
    steps:
      - uses: actions/checkout@v2
      - uses: oven-sh/setup-bun@v2
      - run: bun install
      - run: bunx turbo build --filter=@typebot.io/react...
      - run: cd packages/embeds/react && bun publish --access public
