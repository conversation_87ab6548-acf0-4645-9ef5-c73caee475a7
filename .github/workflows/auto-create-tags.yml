name: Create Tag

on:
  push:
    branches:
      - main

permissions:
  contents: write

jobs:
  create-tag:
    runs-on: ubuntu-latest
    env:
      GITHUB_TOKEN: "${{ secrets.CONTENTS_RW_PAT }}"
    steps:
      - uses: actions/checkout@v2
      - name: "Create main tag"
        id: "main"
        uses: butlerlogic/action-autotag@1.1.2
        with:
          tag_prefix: "v"

      - name: "Create latest tag"
        if: ${{ contains(steps.main.outputs.tagname, 'v') }}
        uses: EndBug/latest-tag@latest

      - name: "Create js tag"
        uses: butlerlogic/action-autotag@1.1.2
        with:
          root: "/packages/embeds/js"
          tag_prefix: "js-v"

      - name: "Create react tag"
        uses: butlerlogic/action-autotag@1.1.2
        with:
          root: "/packages/embeds/react"
          tag_prefix: "react-v"

      - name: "Create nextjs tag"
        uses: butlerlogic/action-autotag@1.1.2
        with:
          root: "/packages/embeds/nextjs"
          tag_prefix: "nextjs-v"
