name: Publish @typebot.io/js package to NPM

on:
  push:
    tags:
      - "js-v*"

jobs:
  publish:
    runs-on: ubuntu-latest
    env:
      NPM_TOKEN: ${{ secrets.NPM_TOKEN }}
    steps:
      - uses: actions/checkout@v2
      - uses: oven-sh/setup-bun@v2
      - run: bun install
      - run: bunx turbo build --filter=@typebot.io/js...
      - run: cd packages/embeds/js && bun publish --access public
