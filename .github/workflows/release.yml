name: Release new Typebot version

on:
  push:
    tags: ["v*", "next"]

permissions:
  contents: read
  actions: write

jobs:
  build-amd:
    env:
      DATABASE_URL: "postgresql://"
    strategy:
      matrix:
        app: ["builder", "viewer"]
    runs-on: ubuntu-latest
    steps:
      - name: Check out the repo
        uses: actions/checkout@v4

      - name: Extract existing image metadata
        id: image-meta
        uses: docker/metadata-action@v5
        with:
          images: baptistearno/typebot-${{ matrix.app }}

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Log in to Docker Hub
        uses: docker/login-action@v2
        with:
          username: ${{ secrets.DOCKER_USERNAME }}
          password: ${{ secrets.DOCKER_PASSWORD }}

      - name: Build image
        id: docker_build
        uses: docker/build-push-action@v6
        with:
          context: .
          labels: ${{ steps.image-meta.outputs.labels }}
          platforms: linux/amd64
          cache-from: type=gha,scope=${{ matrix.app }}-amd
          cache-to: type=gha,scope=${{ matrix.app }}-amd,mode=max
          build-args: |
            SCOPE=${{ matrix.app }}
          outputs: type=image,name=baptistearno/typebot-${{ matrix.app }},push-by-digest=true,name-canonical=true,push=true

      - name: Export digest
        run: |
          mkdir -p /tmp/digests/${{ matrix.app }}
          digest="${{ steps.docker_build.outputs.digest }}"
          touch "/tmp/digests/${{ matrix.app }}/${digest#sha256:}"

      - name: Upload digest
        uses: actions/upload-artifact@v4
        with:
          name: digests-amd-${{ matrix.app }}
          path: /tmp/digests/${{matrix.app}}/*
          if-no-files-found: error
          retention-days: 1

  build-arm:
    env:
      DATABASE_URL: "postgresql://"
    strategy:
      matrix:
        app: ["builder", "viewer"]
    runs-on: buildjet-8vcpu-ubuntu-2204-arm
    steps:
      - name: Check out the repo
        uses: actions/checkout@v4

      - name: Extract existing image metadata
        id: image-meta
        uses: docker/metadata-action@v5
        with:
          images: baptistearno/typebot-${{ matrix.app }}

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
        # Specify the latest buildkit version because the default one on arm uses deprecated GH cache API
        with:
          version: latest
          driver-opts: image=moby/buildkit:master

      - name: Log in to Docker Hub
        uses: docker/login-action@v2
        with:
          username: ${{ secrets.DOCKER_USERNAME }}
          password: ${{ secrets.DOCKER_PASSWORD }}

      - name: Build image
        id: docker_build
        uses: docker/build-push-action@v6
        with:
          context: .
          labels: ${{ steps.image-meta.outputs.labels }}
          platforms: linux/arm64
          cache-from: type=gha,scope=${{ matrix.app }}-arm
          cache-to: type=gha,scope=${{ matrix.app }}-arm,mode=max
          build-args: |
            SCOPE=${{ matrix.app }}
          outputs: type=image,name=baptistearno/typebot-${{ matrix.app }},push-by-digest=true,name-canonical=true,push=true

      - name: Export digest
        run: |
          mkdir -p /tmp/digests/${{ matrix.app }}
          digest="${{ steps.docker_build.outputs.digest }}"
          touch "/tmp/digests/${{ matrix.app }}/${digest#sha256:}"

      - name: Upload digest
        uses: actions/upload-artifact@v4
        with:
          name: digests-arm-${{ matrix.app }}
          path: /tmp/digests/${{matrix.app}}/*
          if-no-files-found: error
          retention-days: 1

  merge-and-push:
    if: ${{ github.event_name != 'pull_request' }}
    runs-on: ubuntu-latest
    needs:
      - build-amd
      - build-arm
    strategy:
      matrix:
        app: ["builder", "viewer"]
    steps:
      - name: Download AMD digests
        uses: actions/download-artifact@v4
        with:
          name: digests-arm-${{ matrix.app }}
          path: /tmp/digests/${{ matrix.app }}
      - name: Download AMD digests
        uses: actions/download-artifact@v4
        with:
          name: digests-amd-${{ matrix.app }}
          path: /tmp/digests/${{ matrix.app }}
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
      - name: Docker meta
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: baptistearno/typebot-${{ matrix.app }}
          tags: |
            type=ref,event=branch
            type=semver,pattern={{version}}
            type=semver,pattern={{major}}.{{minor}}
            type=semver,pattern={{major}}
            next
      - name: Login to Docker Hub
        uses: docker/login-action@v2
        with:
          username: ${{ secrets.DOCKER_USERNAME }}
          password: ${{ secrets.DOCKER_PASSWORD }}
      - name: Create manifest list and push
        working-directory: /tmp/digests/${{ matrix.app }}
        run: |
          docker buildx imagetools create $(jq -cr '.tags | map("-t " + .) | join(" ")' <<< "$DOCKER_METADATA_OUTPUT_JSON") \
            $(printf 'baptistearno/typebot-${{ matrix.app }}@sha256:%s ' *)
      - name: Inspect image
        run: |
          docker buildx imagetools inspect baptistearno/typebot-${{ matrix.app }}:${{ steps.meta.outputs.version }}

  create-release:
    name: Create Release
    runs-on: ubuntu-latest
    permissions:
      contents: write
    needs: merge-and-push
    if: startsWith(github.ref, 'refs/tags/v')
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      - name: Extract body from changelog
        run: |
          NEW_VERSION=$(echo '${{ github.ref }}' | sed 's/refs\/tags\/v//')
          echo $NEW_VERSION
          sed -n -e "/## ${NEW_VERSION}/,/<a/ p" ./CHANGELOG.md | sed -e '1,2d' | sed -e '$d' | sed -e '$d' > extractedBody.md
      - name: Create Release
        uses: ncipollo/release-action@v1
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          bodyFile: "extractedBody.md"
