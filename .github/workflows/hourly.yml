name: Hourly job

on:
  schedule:
    - cron: "0 * * * *"

jobs:
  send:
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: ./packages/scripts
    env:
      DATABASE_URL: "${{ secrets.DATABASE_URL }}"
      DATABASE_URL_REPLICA: "${{ secrets.DATABASE_URL_REPLICA }}"
      ENCRYPTION_SECRET: "${{ secrets.ENCRYPTION_SECRET }}"
      NEXTAUTH_URL: "${{ secrets.NEXTAUTH_URL }}"
      NEXT_PUBLIC_VIEWER_URL: "${{ secrets.NEXT_PUBLIC_VIEWER_URL }}"
      NEXT_PUBLIC_POSTHOG_KEY: "${{ secrets.NEXT_PUBLIC_POSTHOG_KEY }}"
      NEXT_PUBLIC_POSTHOG_HOST: "${{ secrets.NEXT_PUBLIC_POSTHOG_HOST }}"
      SMTP_USERNAME: "${{ secrets.SMTP_USERNAME }}"
      SMTP_PASSWORD: "${{ secrets.SMTP_PASSWORD }}"
      SMTP_HOST: "${{ secrets.SMTP_HOST }}"
      SMTP_PORT: "${{ secrets.SMTP_PORT }}"
      NEXT_PUBLIC_SMTP_FROM: "${{ secrets.NEXT_PUBLIC_SMTP_FROM }}"
      STRIPE_SECRET_KEY: "${{ secrets.STRIPE_SECRET_KEY }}"
      STRIPE_STARTER_PRICE_ID: "${{ secrets.STRIPE_STARTER_PRICE_ID }}"
      STRIPE_STARTER_CHATS_PRICE_ID: "${{ secrets.STRIPE_STARTER_CHATS_PRICE_ID }}"
      STRIPE_PRO_PRICE_ID: "${{ secrets.STRIPE_PRO_PRICE_ID }}"
      STRIPE_PRO_CHATS_PRICE_ID: "${{ secrets.STRIPE_PRO_CHATS_PRICE_ID }}"
    steps:
      - uses: actions/checkout@v2
      - uses: oven-sh/setup-bun@v2
      - run: bun install
      - run: bunx turbo run cron:hourly
