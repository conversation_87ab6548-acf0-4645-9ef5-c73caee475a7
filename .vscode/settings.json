{"editor.formatOnSave": true, "editor.tabSize": 2, "typescript.updateImportsOnFileMove.enabled": "always", "typescript.tsdk": "node_modules/typescript/lib", "typescript.preferences.autoImportFileExcludePatterns": ["next/router.d.ts", "next/dist/client/router.d.ts"], "biome.enabled": true, "[javascript]": {"editor.defaultFormatter": "biomejs.biome"}, "[javascriptreact]": {"editor.defaultFormatter": "biomejs.biome"}, "[typescript]": {"editor.defaultFormatter": "biomejs.biome"}, "[typescriptreact]": {"editor.defaultFormatter": "biomejs.biome"}, "[json]": {"editor.defaultFormatter": "biomejs.biome"}, "[css]": {"editor.defaultFormatter": "biomejs.biome"}, "editor.codeActionsOnSave": {"quickfix.biome": "explicit", "source.organizeImports.biome": "explicit"}, "eslint.enable": false, "npm.packageManager": "bun", "i18n-ally.localesPaths": ["apps/builder/src/i18n"], "i18n-ally.keystyle": "flat", "i18n-ally.displayLanguage": "en", "i18n-ally.sortKeys": true, "i18n-ally.enabledFrameworks": ["custom"], "search.exclude": {"**/deprecated/**": true}}